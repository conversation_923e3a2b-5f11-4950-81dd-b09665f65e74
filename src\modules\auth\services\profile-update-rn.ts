import {PATCH} from '../../../lib/http-methods';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AxiosError, AxiosHeaders} from 'axios';

type AuthResponse = {
  status: number;
  ok: boolean;
  error?: string;
};

export async function updateUserProfile(data: {
  name: string;
}): Promise<AuthResponse> {
  try {
    const token = await AsyncStorage.getItem('access');
    const headers = {
      Authorization: `Bearer ${token}`,
    } as AxiosHeaders;

    await PATCH(`/users/me`, headers, data);

    return {status: 200, ok: true};
  } catch (error) {
    const axiosError = error as AxiosError;
    const responseError: AuthResponse = {
      status: axiosError?.response?.status as number,
      error: '',
      ok: false,
    };

    if (responseError.status === 400) {
      responseError.error = 'Invalid data provided';
    } else if (responseError.status === 401) {
      responseError.error = 'Unauthorized - please login again';
    } else if (responseError.status === 404) {
      responseError.error = 'User not found';
    } else {
      responseError.error = 'An unexpected error occurred';
    }

    return responseError;
  }
}
