import React, {useState} from 'react';
import {TouchableOpacity, View, Image} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {text} from '../text';
import {components} from '../components';
import {useAppNavigation} from '../hooks';
import useAddressCreation from '../modules/auth/hooks/use-address-creation-rn';

const AddANewAddress: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const {createAddress, isCreating, createError, createSuccess, reset} =
    useAddressCreation();

  const [selected, setSelected] = useState(false);
  const [formData, setFormData] = useState({
    type: 'Home' as 'Home' | 'Work' | 'Other',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
  });

  useEffect(() => {
    if (createSuccess) {
      Alert.alert('Success', 'Address created successfully!', [
        {
          text: 'OK',
          onPress: () => {
            reset();
            navigation.goBack();
          },
        },
      ]);
    }
  }, [createSuccess, navigation, reset]);

  useEffect(() => {
    if (createError) {
      Alert.alert('Error', 'Failed to create address. Please try again.');
    }
  }, [createError]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({...prev, [field]: value}));
  };

  const handleSubmit = () => {
    if (!formData.address.trim()) {
      Alert.alert('Error', 'Please enter an address');
      return;
    }

    const addressData: CreateAddressData = {
      type: formData.type,
      address: formData.address,
      city: formData.city,
      state: formData.state,
      zipCode: formData.zipCode,
      country: formData.country,
      isDefault: selected,
    };

    createAddress(addressData);
  };

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Add a new address' goBack={true} />;
  };

  const renderMap = () => {
    return (
      <View
        style={{
          marginTop: 10,
          flex: 1,
          paddingLeft: 20,
        }}
      >
        <Image
          source={{uri: 'https://george-fx.github.io/kastelli/map/01.jpg'}}
          style={{flex: 1}}
          resizeMode='contain'
        />
      </View>
    );
  };

  const renderContent = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 40,
          paddingBottom: 20,
        }}
        enableOnAndroid={true}
        showsVerticalScrollIndicator={false}
        style={{flexGrow: 0}}
      >
        <components.InputField
          label='Address Type'
          placeholder='Home'
          value={formData.type}
          onChangeText={(value) => handleInputChange('type', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Address'
          placeholder='Enter your address'
          value={formData.address}
          onChangeText={(value) => handleInputChange('address', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='City'
          placeholder='Enter city'
          value={formData.city}
          onChangeText={(value) => handleInputChange('city', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='State'
          placeholder='Enter state'
          value={formData.state}
          onChangeText={(value) => handleInputChange('state', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Zip Code'
          placeholder='Enter zip code'
          value={formData.zipCode}
          onChangeText={(value) => handleInputChange('zipCode', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Country'
          placeholder='Enter country'
          value={formData.country}
          onChangeText={(value) => handleInputChange('country', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <TouchableOpacity
          style={{marginBottom: 10, flexDirection: 'row', alignItems: 'center'}}
          onPress={() => setSelected(!selected)}
        >
          <components.Checkbox active={selected} />
          <text.T14
            style={{
              marginLeft: 10,
            }}
          >
            Set as default address
          </text.T14>
        </TouchableOpacity>
      </KeyboardAwareScrollView>
    );
  };

  const renderButton = () => {
    return (
      <components.Button
        title={isCreating ? 'Saving...' : 'Save Address'}
        onPress={handleSubmit}
        disabled={isCreating}
        containerStyle={{
          margin: 20,
        }}
      />
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderMap()}
      {renderContent()}
      {renderButton()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default AddANewAddress;
