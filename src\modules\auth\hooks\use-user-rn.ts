import {useQuery} from '@tanstack/react-query';
import {useEffect, useState} from 'react';
import useUserStore from '../store/user-store';
import {retrieveUserDetails} from '../services/user-details-extraction-rn';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function useUserRN() {
  const [hasToken, setHasToken] = useState(false);

  // Check if token exists on mount
  useEffect(() => {
    const checkToken = async () => {
      const token = await AsyncStorage.getItem('access');

      setHasToken(!!token);
    };
    checkToken();
  }, []);

  const {data, isLoading, error} = useQuery({
    queryKey: ['user-data-rn'],
    queryFn: () => retrieveUserDetails(),
    retry: 1,
    enabled: hasToken,
  });

  const {setUser, setIsLoading: setUserIsLoading} = useUserStore(
    (store) => store,
  );

  useEffect(() => {
    if (data) {
      setUser(data);
    }
  }, [data, setUser]);

  useEffect(() => {
    setUserIsLoading(isLoading);
  }, [isLoading, setUserIsLoading]);

  // Function to refresh token check (call this after login)
  const refreshTokenCheck = async () => {
    const token = await AsyncStorage.getItem('access');
    setHasToken(!!token);
  };

  return {
    user: data ? data : null,
    isLoading,
    refreshTokenCheck,
  };
}
