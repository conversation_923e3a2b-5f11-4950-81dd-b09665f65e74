import {useQuery} from '@tanstack/react-query';
import {useEffect, useState} from 'react';
import useUserStore from '../store/user-store';
import {retrieveUserDetails} from '../services/user-details-extraction-rn';
import tokenManager from '../utils/token-manager-rn';

export default function useUserRN() {
  const [hasToken, setHasToken] = useState(false);
  const [isTokenLoading, setIsTokenLoading] = useState(true);

  // Check if token exists on mount
  useEffect(() => {
    const checkToken = async () => {
      try {
        setIsTokenLoading(true);
        const hasValidToken = await tokenManager.hasTokens();
        setHasToken(hasValidToken);
      } catch (error) {
        console.error('Error checking token:', error);
        setHasToken(false);
      } finally {
        setIsTokenLoading(false);
      }
    };
    checkToken();
  }, []);

  const {data, isLoading, error} = useQuery({
    queryKey: ['user-data-rn'],
    queryFn: () => retrieveUserDetails(),
    retry: 1,
    enabled: hasToken && !isTokenLoading,
  });

  const {setUser, setIsLoading: setUserIsLoading} = useUserStore(
    (store) => store,
  );

  useEffect(() => {
    if (data) {
      setUser(data);
    }
  }, [data, setUser]);

  useEffect(() => {
    setUserIsLoading(isLoading);
  }, [isLoading, setUserIsLoading]);

  // Function to refresh token check (call this after login)
  const refreshTokenCheck = async () => {
    try {
      setIsTokenLoading(true);
      const hasValidToken = await tokenManager.hasTokens();
      setHasToken(hasValidToken);
    } catch (error) {
      console.error('Error refreshing token check:', error);
      setHasToken(false);
    } finally {
      setIsTokenLoading(false);
    }
  };

  return {
    user: data ? data : null,
    isLoading: isLoading || isTokenLoading,
    refreshTokenCheck,
    hasToken,
    isTokenLoading,
  };
}
