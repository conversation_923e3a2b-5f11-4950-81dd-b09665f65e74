import {useState} from 'react';
import useCodeVerification from './use-code-verification';
import {sendResetPasswordEmail} from '../services/reset-password/email-submition';
import {resetPassword} from '../services/reset-password/password-submition';
import {
  validateEmail,
  validatePasswordConfirmation,
} from '../utils/validation-rn';

export default function useResetPassword() {
  const [step, setStep] = useState<'email' | 'code' | 'password'>('email');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [startPasswordStep, setStartPasswordStep] = useState(false);
  const [warning, setWarning] = useState({
    email: '',
    generalWarning: '',
  });
  const [passwordWarning, setPasswordWarning] = useState({
    password: '',
    confirmationPassword: '',
    generalWarning: '',
  });
  const {verifyCode, displayedTimer, startTimer, code, setCode} =
    useCodeVerification();
  const [isLoading, setIsLoading] = useState(false);

  function submitEmail() {
    const emailValidation = validateEmail(email);
    setEmail(email);
    if (!emailValidation.isValid) {
      setWarning({
        email: emailValidation.message,
        generalWarning: emailValidation.message,
      });
    } else {
      setIsLoading(true);
      sendResetPasswordEmail(email).then((res) => {
        if (!res.ok) {
          setWarning({
            email: '',
            generalWarning: 'Email not found or server error',
          });
        } else {
          setStep('code');
          startTimer();
          if (warning.email !== '' || warning.generalWarning !== '')
            setWarning({
              email: '',
              generalWarning: '',
            });
        }
        setIsLoading(false);
      });
    }
  }

  function submitCode() {
    setIsLoading(true);
    console.log(email, code);
    verifyCode(email, code).then((res) => {
      if (res.verified) {
        setStep('password');
        setTimeout(() => {
          setStartPasswordStep(true);
        }, 500);
      } else
        setWarning({
          email: '',
          generalWarning: res.message,
        });

      setIsLoading(false);
    });
  }

  function submitPassword() {
    const passwordValidation = validatePasswordConfirmation(
      password,
      confirmPassword,
    );

    if (!passwordValidation.isValid) {
      setPasswordWarning({
        password: '',
        confirmationPassword: passwordValidation.message,
        generalWarning: passwordValidation.message,
      });
    } else {
      setIsLoading(true);

      //submit verified password on server side
      resetPassword({
        email,
        code,
        password,
      }).then((res) => {
        setIsLoading(false);

        if (res.ok) {
          // Reset password successful - navigate back or show success
          setStep('email'); // Reset to initial state
          setEmail('');
          setPassword('');
          setConfirmPassword('');
        } else {
          setPasswordWarning({
            password: '',
            confirmationPassword: '',
            generalWarning: 'Invalid code or server error',
          });
        }
      });
    }
  }

  return {
    step,
    warning,
    passwordWarning,
    submitEmail,
    displayedTimer,
    submitCode,
    code,
    setCode,
    email,
    setEmail,
    password,
    setPassword,
    confirmPassword,
    setConfirmPassword,
    submitPassword,
    startPasswordStep,
    isLoading,
    setStep,
  };
}
