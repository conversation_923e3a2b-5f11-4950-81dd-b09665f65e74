import {GET} from '../../../lib/http-methods';
import {AxiosError} from 'axios';
import {UserDataType, UserResponseDataType} from '../types';
import {castToUserType} from '../utils/data-utils/types-casting/user';
import tokenManager from '../utils/token-manager-rn';
import tokenRefreshService from './refresh-token-rn';

export async function retrieveUserDetails(): Promise<UserDataType | null> {
  try {
    // Use token refresh service to handle automatic token refresh on 401 errors
    return await tokenRefreshService.executeWithTokenRefresh(async () => {
      const header = await tokenManager.getAuthHeader();

      if (!('Authorization' in header)) {
        console.log('No access token available for user details');
        return null;
      }

      const res = await GET(`/users/me`, header);

      if (!res.data) {
        console.log('No user data received from API');
        return null;
      }

      const userData = res.data as UserResponseDataType;
      return castToUserType(userData);
    });
  } catch (error) {
    const axiosError = error as AxiosError;

    console.error('Failed to retrieve user details:', error);

    // If it's a 401 error, tokens should already be cleared by the refresh service
    if (axiosError.response?.status === 401) {
      console.log('User authentication failed, tokens cleared');
      return null;
    }

    // For other errors, log but don't clear tokens
    if (axiosError.response?.status) {
      console.log(
        `API error ${axiosError.response.status}:`,
        axiosError.response.data,
      );
    }

    return null;
  }
}
