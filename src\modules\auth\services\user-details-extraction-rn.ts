import {GET} from '../../../lib/http-methods';
import {AxiosError} from 'axios';
import {UserDataType, UserResponseDataType} from '../types';
import {castToUserType} from '../utils/data-utils/types-casting/user';
import AsyncStorage from '@react-native-async-storage/async-storage';

export async function retrieveUserDetails(): Promise<UserDataType | null> {
  try {
    const access = await AsyncStorage.getItem('access');

    if (!access) {
      return null;
    }

    const header = {
      Authorization: `Bearer ${access}`,
    };

    const res = await GET(`/users/me`, header);

    if (!res.data) {
      return null;
    }

    const userData = res.data as UserResponseDataType;

    return castToUserType(userData);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      await AsyncStorage.removeItem('access');
      await AsyncStorage.removeItem('refresh');
      return null;
    }

    return null;
  }
}
