import {signIn} from '../services/sign-in';
import {signUp} from '../services/sign-up';
import {AuthFormData, UserSignInType} from '../types';
import {validateSignInForm, validateSignUpForm} from '../utils/validation-rn';
import {useState} from 'react';

export default function useAuthRN() {
  const [isLoading, setIsLoading] = useState(false);
  const [warning, setWarning] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    confirmPassword: '',
    generalWarning: '',
  });

  async function submitSignIn(
    data: UserSignInType,
  ): Promise<{success: boolean; message?: string}> {
    const validationResult = validateSignInForm(data);

    if (!validationResult.isValid) {
      setWarning(validationResult.warnings);
      return {success: false};
    }

    setIsLoading(true);

    try {
      const res = await signIn(data);

      console.log(res);

      if (res.ok) {
        // Clear warnings on success
        setWarning({
          email: '',
          password: '',
          firstName: '',
          lastName: '',
          confirmPassword: '',
          generalWarning: '',
        });
        setIsLoading(false);
        return {success: true};
      } else {
        // Import the warning function at the top of the file
        const {
          getSignInStatusWarning,
        } = require('../utils/warnings/server-response-warning');
        const warningMessage = getSignInStatusWarning(res.status);
        setWarning({
          email: '',
          password: '',
          firstName: '',
          lastName: '',
          confirmPassword: '',
          generalWarning: warningMessage,
        });
        setIsLoading(false);
        return {success: false};
      }
    } catch (error) {
      setWarning({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        confirmPassword: '',
        generalWarning: 'An unexpected error occurred',
      });
      setIsLoading(false);
      return {success: false};
    }
  }

  async function submitSignUp(
    data: AuthFormData,
  ): Promise<{success: boolean; message?: string}> {
    const validationResult = validateSignUpForm(data);

    if (!validationResult.isValid) {
      setWarning(validationResult.warnings);
      return {success: false};
    }

    setIsLoading(true);

    try {
      const res = await signUp(data);

      if (res.ok) {
        // Clear warnings on success
        setWarning({
          email: '',
          password: '',
          firstName: '',
          lastName: '',
          confirmPassword: '',
          generalWarning: '',
        });
        setIsLoading(false);
        return {success: true};
      } else {
        const {
          getSignUpStatusWarning,
        } = require('../utils/warnings/server-response-warning');
        const warningMessages = getSignUpStatusWarning(res.status);
        setWarning({
          email: warningMessages.email || '',
          password: '',
          firstName: '',
          lastName: '',
          confirmPassword: '',
          generalWarning: warningMessages.generalWarning,
        });
        setIsLoading(false);
        return {success: false};
      }
    } catch (error) {
      setWarning({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        confirmPassword: '',
        generalWarning: 'An unexpected error occurred',
      });
      setIsLoading(false);
      return {success: false};
    }
  }

  function clearWarnings() {
    setWarning({
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      confirmPassword: '',
      generalWarning: '',
    });
  }

  return {
    submitSignIn,
    submitSignUp,
    warning,
    isLoading,
    clearWarnings,
  };
}
