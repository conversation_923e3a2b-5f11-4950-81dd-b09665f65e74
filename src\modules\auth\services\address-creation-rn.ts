import {POST} from '../../../lib/http-methods';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AddressType} from '../types/address';

export interface CreateAddressData {
  type: 'Home' | 'Work' | 'Other';
  address: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  isDefault?: boolean;
}

export async function createAddressOnServerSide(
  data: CreateAddressData,
): Promise<{success: boolean; address?: AddressType; error?: string}> {
  try {
    const token = await AsyncStorage.getItem('access');

    if (!token) {
      throw new Error('No access token found');
    }

    const response = await POST(
      '/users/addresses',
      {
        Authorization: `Bearer ${token}`,
      },
      {
        type: data.type,
        full_address: data.address,
        city: data.city,
        state: data.state,
        zip_code: data.zipCode,
        country: data.country,
        is_default: data.isDefault || false,
      },
    );

    // Map server response to AddressType format
    const address: AddressType = {
      id: response.data.id.toString(),
      type: response.data.type || data.type,
      address: response.data.full_address || data.address,
      city: response.data.city,
      state: response.data.state,
      zipCode: response.data.zip_code,
      country: response.data.country,
      isDefault: response.data.is_default || false,
    };

    return {success: true, address};
  } catch (error) {
    console.error('Error creating address:', error);
    return {success: false, error: 'Failed to create address'};
  }
}
