import {GET} from '../../../lib/http-methods';
import {
  OrderType,
  OrderItemType,
} from '../../../components/order/OrderContainer';
import tokenManager from '../utils/token-manager-rn';
import tokenRefreshService from './refresh-token-rn';

interface OrderItemResponse {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    name: string;
    image: string;
    slug: string;
    items: Array<{
      id: string;
      image: string;
      prices: Array<{
        currency: string;
        realPrice: number;
        promotionalPrice: number;
      }>;
    }>;
  };
}

interface OrderResponse {
  id: string;
  orderNumber: string;
  createdAt: string;
  status: string;
  total: number;
  currency: string;
  shippingAddress?: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  trackingNumber?: string;
  items: OrderItemResponse[];
}

const mapOrderResponseToOrderType = (
  orderResponse: OrderResponse,
): OrderType => {
  const items: OrderItemType[] = orderResponse.items.map((item) => ({
    id: item.product.id,
    name: item.product.name,
    image: item.product.image || item.product.items[0]?.image || '',
    slug: item.product.slug,
    quantity: item.quantity,
    price: item.price,
    currency: orderResponse.currency,
  }));

  const shippingAddress = orderResponse.shippingAddress
    ? `${orderResponse.shippingAddress.street}, ${orderResponse.shippingAddress.city}, ${orderResponse.shippingAddress.postalCode}, ${orderResponse.shippingAddress.country}`
    : undefined;

  return {
    id: orderResponse.id,
    orderNumber: orderResponse.orderNumber,
    date: new Date(orderResponse.createdAt).toLocaleDateString(),
    status: orderResponse.status as OrderType['status'],
    items,
    total: orderResponse.total,
    currency: orderResponse.currency,
    shippingAddress,
    trackingNumber: orderResponse.trackingNumber,
  };
};

export async function retrieveOrderFromServerSide(
  orderId: string,
): Promise<OrderType> {
  try {
    // Use token refresh service to handle automatic token refresh on 401 errors
    return await tokenRefreshService.executeWithTokenRefresh(async () => {
      const header = await tokenManager.getAuthHeader();

      if (!('Authorization' in header)) {
        console.error('No access token available for order fetch');
        throw new Error('No authentication token found');
      }

      console.log('Fetching order with token available, orderId:', orderId);

      const response = await GET(`/orders/${orderId}`, header);

      console.log('Order API response:', response.status, response.data);

      if (!response.data) {
        throw new Error('Order not found');
      }

      return mapOrderResponseToOrderType(response.data);
    });
  } catch (error: any) {
    console.error('Error fetching order:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url,
      orderId,
    });

    if (error.response?.status === 401) {
      console.log('Authentication failed for order fetch');
    }

    throw error;
  }
}
