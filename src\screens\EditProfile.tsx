import {View, Alert} from 'react-native';
import React, {useEffect, useState} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {components} from '../components';
import {useAppNavigation} from '../hooks';
import useUserRN from '../modules/auth/hooks/use-user-rn';
import useProfileUpdate from '../modules/auth/hooks/use-profile-update-rn';

const EditProfile: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const {user, isLoading} = useUserRN();
  const {updateProfile, isUpdating, updateError, updateSuccess, reset} =
    useProfileUpdate();

  const [username, setUsername] = useState<string>('');

  // Populate form with user data when available
  useEffect(() => {
    if (user) {
      setUsername(user.name || '');
    }
  }, [user]);

  // Handle update success
  useEffect(() => {
    if (updateSuccess) {
      Alert.alert('Success', 'Profile updated successfully!', [
        {
          text: 'OK',
          onPress: () => {
            reset();
            navigation.goBack();
          },
        },
      ]);
    }
  }, [updateSuccess, navigation, reset]);

  // Handle update error
  useEffect(() => {
    if (updateError) {
      Alert.alert('Error', 'Failed to update profile. Please try again.', [
        {
          text: 'OK',
          onPress: () => reset(),
        },
      ]);
    }
  }, [updateError, reset]);

  const renderStatusBar: () => JSX.Element = () => {
    return <components.StatusBar />;
  };

  const renderHeader: () => JSX.Element = () => {
    return <components.Header title='Edit profile' goBack={true} />;
  };

  const renderForm: () => JSX.Element = () => {
    return (
      <View>
        <components.InputField
          label='name'
          placeholder='Enter your name'
          value={username}
          containerStyle={{marginBottom: 20}}
          onChangeText={(text: string) => setUsername(text)}
          check={false}
        />
        <components.InputField
          label='email'
          placeholder='Email address'
          value={user?.email || ''}
          containerStyle={{marginBottom: 20}}
          check={false}
          disabled={true}
        />
      </View>
    );
  };

  const renderButton: () => JSX.Element = () => {
    return (
      <components.Button
        title={isUpdating ? 'Saving...' : 'save changes'}
        onPress={() => {
          if (isUpdating) return; // Prevent multiple calls

          if (username.trim() && username !== user?.name) {
            updateProfile({name: username.trim()});
          } else if (username.trim() === user?.name) {
            Alert.alert('Info', 'No changes to save.');
          } else {
            Alert.alert('Error', 'Please enter a valid name.');
          }
        }}
      />
    );
  };

  const renderContent: () => JSX.Element = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          flexGrow: 1,
          paddingTop: 55,
          paddingBottom: 20,
        }}
      >
        {renderForm()}
        {renderButton()}
      </KeyboardAwareScrollView>
    );
  };

  const renderHomeIndicator: () => JSX.Element = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default EditProfile;
