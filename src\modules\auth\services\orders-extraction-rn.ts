import {GET} from '../../../lib/http-methods';
import {
  OrderType,
  OrderItemType,
} from '../../../components/order/OrderContainer';
import tokenManager from '../utils/token-manager-rn';
import tokenRefreshService from './refresh-token-rn';

interface OrderItemResponse {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    name: string;
    image: string;
    slug: string;
    items: Array<{
      id: string;
      image: string;
      prices: Array<{
        currency: string;
        realPrice: number;
        promotionalPrice: number;
      }>;
    }>;
  };
}

interface OrderResponse {
  id: string;
  orderNumber: string;
  createdAt: string;
  status: string;
  total: number;
  currency: string;
  shippingAddress?: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  trackingNumber?: string;
  items: OrderItemResponse[];
}

const mapOrderResponseToOrderType = (
  orderResponse: OrderResponse,
): OrderType => {
  const items: OrderItemType[] = orderResponse.items.map((item) => ({
    id: item.product.id,
    name: item.product.name,
    image: item.product.image || item.product.items[0]?.image || '',
    slug: item.product.slug,
    quantity: item.quantity,
    price: item.price,
    currency: orderResponse.currency,
  }));

  const shippingAddress = orderResponse.shippingAddress
    ? `${orderResponse.shippingAddress.street}, ${orderResponse.shippingAddress.city}, ${orderResponse.shippingAddress.postalCode}, ${orderResponse.shippingAddress.country}`
    : undefined;

  return {
    id: orderResponse.id,
    orderNumber: orderResponse.orderNumber,
    date: new Date(orderResponse.createdAt).toLocaleDateString(),
    status: orderResponse.status as OrderType['status'],
    items,
    total: orderResponse.total,
    currency: orderResponse.currency,
    shippingAddress,
    trackingNumber: orderResponse.trackingNumber,
  };
};

export async function retrieveOrdersFromServerSide(): Promise<OrderType[]> {
  try {
    return await tokenRefreshService.executeWithTokenRefresh(async () => {
      const header = await tokenManager.getAuthHeader();

      if (!('Authorization' in header)) {
        throw new Error('No authentication token found');
      }
      const response = await GET('/orders', header);

      if (!response.data) {
        return [];
      }
      console.log('Mapped orders:', response.data);

      const mappedOrders = response.data.map(mapOrderResponseToOrderType);
      return mappedOrders;
    });
  } catch (error: any) {
    if (error.response?.status === 404) {
      return [];
    }

    if (error.response?.status === 401) {
      throw new Error('Authentication failed');
    }

    throw error;
  }
}
