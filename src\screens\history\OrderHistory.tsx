import React from 'react';
import {ScrollView, View} from 'react-native';

import {useAppSelector} from '../../hooks';

import _v1 from './versions/_v1';

import {theme} from '../../constants';
import {useAppNavigation} from '../../hooks';
import useOrders from '../../modules/auth/hooks/use-orders-rn';
import {components} from '../../components';
import {text} from '../../text';

const OrderHistory: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const version = useAppSelector((state) => state.appState.orderHistoryVersion);
  const {orders, ordersAreLoading, ordersError} = useOrders();

  const renderStatusBar = (): JSX.Element => {
    return <components.StatusBar />;
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  const renderHeader: () => JSX.Element = () => {
    return <components.Header goBack={true} title='Order history' />;
  };

  const renderContent: () => JSX.Element = () => {
    if (ordersAreLoading) {
      return <components.Loader />;
    }

    if (ordersError) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}
        >
          <text.H4 style={{marginBottom: 10, textAlign: 'center'}}>
            Error loading orders
          </text.H4>
          <text.T16
            style={{textAlign: 'center', color: theme.colors.textColor}}
          >
            Please try again later
          </text.T16>
        </View>
      );
    }

    if (!orders || orders.length === 0) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}
        >
          <text.H4 style={{marginBottom: 10, textAlign: 'center'}}>
            No orders found
          </text.H4>
          <text.T16
            style={{textAlign: 'center', color: theme.colors.textColor}}
          >
            You haven't placed any orders yet
          </text.T16>
        </View>
      );
    }

    return (
      <ScrollView
        contentContainerStyle={{
          paddingTop: 15,
          paddingHorizontal: 20,
          flexGrow: 1,
        }}
      >
        {version === 1 ? (
          orders.map((order) => (
            <components.OrderContainer
              key={order.id}
              order={order}
              onPress={() => navigation.navigate('TrackYourOrder')}
            />
          ))
        ) : (
          <_v1 />
        )}
      </ScrollView>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default OrderHistory;
