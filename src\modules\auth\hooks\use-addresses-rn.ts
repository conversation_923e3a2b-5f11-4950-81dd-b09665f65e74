import {useQuery} from '@tanstack/react-query';
import {AddressType} from '../types/address';
import {retrieveAddressesFromServerSide} from '../services/addresses-extraction-rn';

export default function useAddresses() {
  const {data, isLoading, error, refetch} = useQuery<AddressType[] | null>({
    queryKey: ['addresses'],
    queryFn: () => retrieveAddressesFromServerSide(),
  });

  return {
    addresses: data || [],
    addressesAreLoading: isLoading,
    addressesError: error,
    refetchAddresses: refetch,
  };
}
