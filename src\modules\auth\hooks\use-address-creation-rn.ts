import {useMutation, useQueryClient} from '@tanstack/react-query';
import {
  createAddressOnServerSide,
  CreateAddressData,
} from '../services/address-creation-rn';

export default function useAddressCreation() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (data: CreateAddressData) => createAddressOnServerSide(data),
    onSuccess: () => {
      // Invalidate and refetch addresses
      queryClient.invalidateQueries({queryKey: ['addresses']});
    },
  });

  return {
    createAddress: mutation.mutate,
    isCreating: mutation.isPending,
    createError: mutation.error,
    createSuccess: mutation.isSuccess,
    reset: mutation.reset,
  };
}
