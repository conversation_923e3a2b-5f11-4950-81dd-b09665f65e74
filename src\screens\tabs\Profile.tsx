import {View, TouchableOpacity, ScrollView} from 'react-native';

import {text} from '../../text';
import {svg} from '../../assets/svg';
import {theme, tabs} from '../../constants';
import {components} from '../../components';
import {
  setRefreshToken,
  setAccessToken,
} from '../../store/slices/appStateSlice';
import {useAppNavigation, useAppDispatch} from '../../hooks';
import useUserRN from '../../modules/auth/hooks/use-user-rn';

const Profile = () => {
  const dispatch = useAppDispatch();
  const navigation = useAppNavigation();
  const {user, isLoading} = useUserRN();

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return (
      <components.Header
        burgerIcon={true}
        basket={true}
        title='My profile'
        bottomLine={true}
      />
    );
  };

  const renderTabBar = (): JSX.Element => {
    return (
      <components.TabBar>
        {tabs.map((item, index) => {
          return <components.TabBarItem item={item} key={index} />;
        })}
      </components.TabBar>
    );
  };

  const renderUser = () => {
    if (isLoading) {
      return (
        <View
          style={{
            paddingHorizontal: 20,
            paddingBottom: 30,
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <View
            style={{
              marginRight: 14,
              width: 60,
              height: 60,
              borderRadius: 30,
              backgroundColor: theme.colors.darkBlue,
            }}
          />
          <View style={{flex: 1}}>
            <View
              style={{
                height: 20,
                backgroundColor: theme.colors.darkBlue,
                marginBottom: 8,
                borderRadius: 4,
                width: '60%',
              }}
            />
            <View
              style={{
                height: 16,
                backgroundColor: theme.colors.darkBlue,
                borderRadius: 4,
                width: '80%',
              }}
            />
          </View>
        </View>
      );
    }

    return (
      <TouchableOpacity
        style={{
          paddingHorizontal: 20,
          paddingBottom: 30,
          flexDirection: 'row',
          alignItems: 'center',
        }}
        onPress={() => navigation.navigate('EditProfile')}
      >
        <View style={{flex: 1}}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <text.H5 style={{marginRight: 'auto', marginBottom: 4}}>
              {user?.name || 'Guest User'}
            </text.H5>
            <svg.EditSvg />
          </View>
          <text.T14>{user?.email || 'No email available'}</text.T14>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMenu = (): JSX.Element => {
    return (
      <View style={{paddingLeft: 20, flex: 1}}>
        <components.ProfileItem
          icon={<svg.ServerSvg />}
          title='My orders'
          onPress={() => {
            navigation.navigate('OrderHistory');
          }}
        />
        <components.ProfileItem
          icon={<svg.CreditCardSvg />}
          title='Payment method'
          onPress={() => {
            navigation.navigate('PaymentMethod');
          }}
        />
        <components.ProfileItem
          icon={<svg.MapPinSvg />}
          title='Delivery address'
          onPress={() => {
            navigation.navigate('MyAddress');
          }}
        />
        <components.ProfileItem
          icon={<svg.GiftSvg />}
          title='Promocodes & gift cards'
          onPress={() => {
            navigation.navigate('MyPromocodes');
          }}
        />
        <components.ProfileItem
          icon={<svg.LogOutSvg />}
          title='Sign out'
          onPress={() => {
            dispatch(setRefreshToken(null));
            dispatch(setAccessToken(null));
          }}
        />
      </View>
    );
  };

  const renderContent = (): JSX.Element => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingTop: 55,
          paddingBottom: 20,
        }}
      >
        {renderUser()}
        {renderMenu()}
      </ScrollView>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderTabBar()}
      {/* {renderSignOutModal()} */}
    </components.SmartView>
  );
};

export default Profile;
