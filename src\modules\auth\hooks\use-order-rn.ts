import {useQuery} from '@tanstack/react-query';
import {OrderType} from '../../../components/order/OrderContainer';
import {retrieveOrderFromServerSide} from '../services/order-extraction-rn';

interface UseOrderParams {
  orderId: string;
}

export default function useOrder({orderId}: UseOrderParams) {
  const {data, isLoading, error, refetch} = useQuery<OrderType | null>({
    queryKey: ['order', orderId],
    queryFn: () => retrieveOrderFromServerSide(orderId),
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    order: data,
    orderIsLoading: isLoading,
    orderError: error,
    refetchOrder: refetch,
  };
}
