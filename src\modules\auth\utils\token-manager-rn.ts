import AsyncStorage from '@react-native-async-storage/async-storage';
import {store} from '../../../store/store';
import {
  setAccessToken,
  setRefreshToken,
} from '../../../store/slices/appStateSlice';

export interface TokenPair {
  access: string | null;
  refresh: string | null;
}

class TokenManagerRN {
  private static instance: TokenManagerRN;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  private constructor() {}

  static getInstance(): TokenManagerRN {
    if (!TokenManagerRN.instance) {
      TokenManagerRN.instance = new TokenManagerRN();
    }
    return TokenManagerRN.instance;
  }

  /**
   * Initialize token manager by loading tokens from AsyncStorage to Redux store
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._performInitialization();
    await this.initializationPromise;
  }

  private async _performInitialization(): Promise<void> {
    try {
      const [accessToken, refreshToken] = await Promise.all([
        AsyncStorage.getItem('access'),
        AsyncStorage.getItem('refresh'),
      ]);

      // Sync with Redux store
      store.dispatch(setAccessToken(accessToken));
      store.dispatch(setRefreshToken(refreshToken));

      this.isInitialized = true;
      console.log('TokenManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize TokenManager:', error);
      this.isInitialized = true; // Mark as initialized even on error to prevent infinite loops
    }
  }

  /**
   * Get tokens with automatic initialization
   */
  async getTokens(): Promise<TokenPair> {
    await this.initialize();

    try {
      const [access, refresh] = await Promise.all([
        AsyncStorage.getItem('access'),
        AsyncStorage.getItem('refresh'),
      ]);

      return {access, refresh};
    } catch (error) {
      console.error('Failed to get tokens:', error);
      return {access: null, refresh: null};
    }
  }

  /**
   * Get access token only
   */
  async getAccessToken(): Promise<string | null> {
    const {access} = await this.getTokens();
    return access;
  }

  /**
   * Get refresh token only
   */
  async getRefreshToken(): Promise<string | null> {
    const {refresh} = await this.getTokens();
    return refresh;
  }

  /**
   * Set tokens in both AsyncStorage and Redux store
   */
  async setTokens(tokens: {access: string; refresh: string}): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.setItem('access', tokens.access),
        AsyncStorage.setItem('refresh', tokens.refresh),
      ]);

      // Sync with Redux store
      store.dispatch(setAccessToken(tokens.access));
      store.dispatch(setRefreshToken(tokens.refresh));

      console.log('Tokens set successfully');
    } catch (error) {
      console.error('Failed to set tokens:', error);
      throw error;
    }
  }

  /**
   * Update only access token
   */
  async setAccessToken(accessToken: string): Promise<void> {
    try {
      await AsyncStorage.setItem('access', accessToken);
      store.dispatch(setAccessToken(accessToken));
      console.log('Access token updated successfully');
    } catch (error) {
      console.error('Failed to set access token:', error);
      throw error;
    }
  }

  /**
   * Clear all tokens
   */
  async clearTokens(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem('access'),
        AsyncStorage.removeItem('refresh'),
      ]);

      // Clear from Redux store
      store.dispatch(setAccessToken(null));
      store.dispatch(setRefreshToken(null));

      console.log('Tokens cleared successfully');
    } catch (error) {
      console.error('Failed to clear tokens:', error);
      throw error;
    }
  }

  /**
   * Check if user has valid tokens
   */
  async hasValidTokens(): Promise<boolean> {
    const {access, refresh} = await this.getTokens();
    return !!(access && refresh);
  }

  async hasTokens(): Promise<boolean> {
    const {access} = await this.getTokens();
    return !!access;
  }

  async getAuthHeader(): Promise<{Authorization: string} | {}> {
    const accessToken = await this.getAccessToken();

    if (!accessToken) {
      console.warn('No access token available for authorization header');
      return {};
    }

    return {
      Authorization: `Bearer ${accessToken}`,
    };
  }
}

// Export singleton instance
export const tokenManager = TokenManagerRN.getInstance();
export default tokenManager;
