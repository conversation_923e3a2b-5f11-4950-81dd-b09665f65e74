import {View} from 'react-native';
import React, {useState, useEffect} from 'react';
import ParsedText from 'react-native-parsed-text';

import {text} from '../../text';
import {theme} from '../../constants';
import {components} from '../../components';
import {useAppNavigation} from '../../hooks';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import useAuthRN from '../../modules/auth/hooks/use-auth-rn';
import {WarningManagerRN} from '../../modules/auth/utils/warning-manager-rn';

const SignUp: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const {submitSignUp, warning, isLoading} = useAuthRN();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const handleSignUp = async () => {
    const result = await submitSignUp({
      firstName,
      lastName,
      email,
      password,
      confirmPassword,
    });

    if (result.success) {
      WarningManagerRN.showSignUpSuccess();
      navigation.navigate('SignIn');
    }
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      setFirstName('');
      setLastName('');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
    });

    return unsubscribe;
  }, [navigation]);

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header goBack={true} />;
  };

  const renderContent = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          flexGrow: 1,
          padding: 20,
          justifyContent: 'center',
        }}
        enableOnAndroid={true}
      >
        <text.H1 style={{marginBottom: 40}}>Sign up</text.H1>
        <components.InputField
          label='First Name'
          placeholder='Callie'
          containerStyle={{marginBottom: warning.firstName ? 40 : 20}}
          onChangeText={(text) => setFirstName(text)}
          value={firstName}
          checkIcon={true}
          warning={warning.firstName}
        />
        <components.InputField
          label='Last Name'
          placeholder='Mosley'
          containerStyle={{marginBottom: warning.lastName ? 40 : 20}}
          onChangeText={(text) => setLastName(text)}
          value={lastName}
          checkIcon={true}
          warning={warning.lastName}
        />
        <components.InputField
          label='Email'
          placeholder='<EMAIL>'
          containerStyle={{marginBottom: warning.email ? 40 : 20}}
          onChangeText={(text) => setEmail(text)}
          value={email}
          checkIcon={true}
          warning={warning.email}
        />
        <components.InputField
          label='password'
          placeholder='••••••••'
          containerStyle={{marginBottom: warning.password ? 40 : 20}}
          onChangeText={(text) => setPassword(text)}
          value={password}
          secureTextEntry={true}
          eyeOffIcon={true}
          warning={warning.password}
        />
        <components.InputField
          label='confirm password'
          placeholder='••••••••'
          containerStyle={{marginBottom: warning.confirmPassword ? 40 : 20}}
          onChangeText={(text) => setConfirmPassword(text)}
          value={confirmPassword}
          eyeOffIcon={true}
          secureTextEntry={true}
          warning={warning.confirmPassword}
        />
        {warning.generalWarning ? (
          <text.T14
            style={{color: '#E82837', marginBottom: 20, textAlign: 'center'}}
          >
            {warning.generalWarning}
          </text.T14>
        ) : null}
        <components.Button
          title={isLoading ? 'Signing up...' : 'Sign up'}
          containerStyle={{marginBottom: 20}}
          onPress={handleSignUp}
        />
        <ParsedText
          style={{...theme.fonts.textStyle_16, color: theme.colors.textColor}}
          parse={[
            {
              pattern: /Sign in./,
              style: {color: theme.colors.mainColor},
              onPress: () => navigation.navigate('SignIn'),
            },
          ]}
        >
          Already have an account? Sign in.
        </ParsedText>
      </KeyboardAwareScrollView>
    );
  };

  const renderFooter = () => {
    return (
      <View style={{padding: 20}}>
        <text.T16 style={{marginBottom: 20}}>
          Sign in with social networks:
        </text.T16>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <components.Facebook onPress={() => console.log('Facebook')} />
          <components.Twitter onPress={() => console.log('Twitter')} />
          <components.Google onPress={() => console.log('Google')} />
        </View>
      </View>
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderFooter()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default SignUp;
