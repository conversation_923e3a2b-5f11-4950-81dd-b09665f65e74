import {useMutation, useQueryClient} from '@tanstack/react-query';
import {updateUserProfile} from '../services/profile-update-rn';
import useUserStore from '../store/user-store';

export default function useProfileUpdate() {
  const queryClient = useQueryClient();
  const {setUser} = useUserStore((store) => store);

  const mutation = useMutation({
    mutationFn: (data: {name: string}) => updateUserProfile(data),
    onSuccess: (response, variables) => {
      if (response.ok) {
        // Update the user store with new name
        const {user} = useUserStore.getState();
        if (user) {
          setUser({...user, name: variables.name});
        }
        
        // Invalidate and refetch user data
        queryClient.invalidateQueries({queryKey: ['user-data-rn']});
      }
    },
  });

  return {
    updateProfile: mutation.mutate,
    isUpdating: mutation.isPending,
    updateError: mutation.error,
    updateSuccess: mutation.isSuccess,
    reset: mutation.reset,
  };
}
