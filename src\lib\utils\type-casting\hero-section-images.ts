import {
  HeroSectionImages,
  HeroSectionImagesInResponse,
  LandingPageContent,
  LandingPageContentInResponseType,
} from '../../../types/landing-page';
import {BASE_URL} from '../../../config/config';

const BACKEND_ADDRESS = BASE_URL;

export default function castToHeroSectionImage(
  image: HeroSectionImagesInResponse,
): HeroSectionImages {
  return {
    computerImage: `${BACKEND_ADDRESS}${image.computerImage}`,
    mobileImage: `${BACKEND_ADDRESS}${image.mobileImage}`,
    link: image.redirectUrl,
  };
}

export function castToLandingPageContentType(
  landingPageContentInResponse: LandingPageContentInResponseType,
): LandingPageContent {
  return {
    id: landingPageContentInResponse.id,
    name: landingPageContentInResponse.name,
    images: landingPageContentInResponse.images.map((image) => ({
      computerImage: `${BACKEND_ADDRESS}${image.computerImage || ''}`,
      mobileImage: `${BACKEND_ADDRESS}${image.mobileImage || ''}`,
      link: image.redirectUrl,
    })),
    sections: landingPageContentInResponse.sections
      ? landingPageContentInResponse.sections.map((section) => ({
          id: section.id,
          title: section.title,
          description: section.description,
        }))
      : [],
  };
}
