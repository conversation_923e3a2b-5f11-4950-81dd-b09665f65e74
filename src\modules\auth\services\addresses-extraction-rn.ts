import {GET} from '../../../lib/http-methods';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AddressType} from '../types/address';

export async function retrieveAddressesFromServerSide(): Promise<
  AddressType[] | null
> {
  try {
    const token = await AsyncStorage.getItem('access');

    if (!token) {
      console.error('No access token found in AsyncStorage');
      throw new Error('No access token found');
    }

    console.log(
      'Fetching addresses with token:',
      token ? 'Token exists' : 'No token',
    );

    const response = await GET('/users/addresses', {
      Authorization: `Bearer ${token}`,
    });

    console.log('Addresses API response:', response.status, response.data);

    // Handle case where response.data might be an object with addresses array
    const addressesData = Array.isArray(response.data)
      ? response.data
      : response.data?.addresses || [];

    const addresses: AddressType[] = addressesData.map((address: any) => ({
      id: address.id.toString(),
      type: address.type || 'Other',
      address: address.full_address || address.address,
      city: address.city,
      state: address.state,
      zipCode: address.zip_code,
      country: address.country,
      isDefault: address.is_default || false,
    }));

    console.log('Mapped addresses:', addresses);
    return addresses;
  } catch (error: any) {
    console.error('Error fetching addresses:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url,
    });

    // If it's a 404, return empty array instead of null to show "no addresses" message
    if (error.response?.status === 404) {
      return [];
    }

    return null;
  }
}
