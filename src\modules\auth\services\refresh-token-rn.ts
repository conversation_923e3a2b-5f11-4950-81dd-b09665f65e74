import {POST} from '../../../lib/http-methods';
import {AxiosHeaders, AxiosResponse, AxiosError} from 'axios';
import tokenManager from '../utils/token-manager-rn';

interface RefreshTokenResponse {
  access: string;
  refresh?: string;
}

class TokenRefreshService {
  private static instance: TokenRefreshService;
  private refreshPromise: Promise<string | null> | null = null;

  private constructor() {}

  static getInstance(): TokenRefreshService {
    if (!TokenRefreshService.instance) {
      TokenRefreshService.instance = new TokenRefreshService();
    }
    return TokenRefreshService.instance;
  }

  /**
   * Refresh access token using refresh token
   * Returns new access token or null if refresh failed
   */
  async refreshToken(): Promise<string | null> {
    // If refresh is already in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this._performRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = null;

    return result;
  }

  private async _performRefresh(): Promise<string | null> {
    try {
      const refreshToken = await tokenManager.getRefreshToken();

      if (!refreshToken) {
        console.warn('No refresh token available');
        await tokenManager.clearTokens();
        return null;
      }

      const headers = {} as AxiosHeaders;

      console.log('Attempting to refresh token...');

      const res: AxiosResponse<RefreshTokenResponse> = await POST(
        '/tokens/refresh',
        headers,
        {token: refreshToken},
      );

      if (res.data?.access) {
        // Update access token
        await tokenManager.setAccessToken(res.data.access);

        // Update refresh token if provided
        if (res.data.refresh) {
          await tokenManager.setTokens({
            access: res.data.access,
            refresh: res.data.refresh,
          });
        }

        console.log('Token refreshed successfully');
        return res.data.access;
      } else {
        console.error('Invalid refresh response: no access token');
        await tokenManager.clearTokens();
        return null;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);

      const axiosError = error as AxiosError;

      // If refresh token is invalid/expired, clear all tokens
      if (
        axiosError.response?.status === 401 ||
        axiosError.response?.status === 403
      ) {
        console.log('Refresh token expired, clearing all tokens');
        await tokenManager.clearTokens();
      }

      return null;
    }
  }

  /**
   * Execute a function with automatic token refresh on 401 errors
   */
  async executeWithTokenRefresh<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 1,
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error;
        const axiosError = error as AxiosError;

        if (axiosError.response?.status === 401 && attempt < maxRetries) {
          console.log(
            `API call failed with 401, attempting token refresh (attempt ${
              attempt + 1
            }/${maxRetries + 1})`,
          );

          const newToken = await this.refreshToken();

          if (!newToken) {
            console.log('Token refresh failed, not retrying');
            break;
          }

          console.log('Token refreshed, retrying API call');
          continue;
        }

        break;
      }
    }

    throw lastError;
  }

  isTokenError(error: any): boolean {
    const axiosError = error as AxiosError;
    return axiosError.response?.status === 401;
  }

  /**
   * Clear refresh promise (useful for testing or manual reset)
   */
  clearRefreshPromise(): void {
    this.refreshPromise = null;
  }
}

// Export singleton instance
export const tokenRefreshService = TokenRefreshService.getInstance();
export default tokenRefreshService;

/**
 * Helper function for backward compatibility
 */
export async function refreshToken(onSuccess?: () => any): Promise<any> {
  const newToken = await tokenRefreshService.refreshToken();

  if (newToken && onSuccess) {
    return onSuccess();
  }

  return newToken ? {access: newToken} : null;
}
