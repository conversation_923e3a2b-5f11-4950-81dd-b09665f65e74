import {useQuery} from '@tanstack/react-query';
import {OrderType} from '../../../components/order/OrderContainer';
import {retrieveOrdersFromServerSide} from '../services/orders-extraction-rn';

export default function useOrders() {
  const {data, isLoading, error, refetch} = useQuery<OrderType[] | null>({
    queryKey: ['orders'],
    queryFn: () => retrieveOrdersFromServerSide(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    orders: data || [],
    ordersAreLoading: isLoading,
    ordersError: error,
    refetchOrders: refetch,
  };
}
