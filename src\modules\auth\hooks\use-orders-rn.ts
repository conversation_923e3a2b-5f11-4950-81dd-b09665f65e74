import {useQuery} from '@tanstack/react-query';
import {useEffect, useState} from 'react';
import {OrderType} from '../../../components/order/OrderContainer';
import {retrieveOrdersFromServerSide} from '../services/orders-extraction-rn';
import tokenManager from '../utils/token-manager-rn';

export default function useOrders() {
  const [hasToken, setHasToken] = useState(false);
  const [isTokenLoading, setIsTokenLoading] = useState(true);

  // Check if token exists on mount
  useEffect(() => {
    const checkToken = async () => {
      try {
        setIsTokenLoading(true);
        const hasValidToken = await tokenManager.hasTokens();
        setHasToken(hasValidToken);
      } catch (error) {
        console.error('Error checking token for orders:', error);
        setHasToken(false);
      } finally {
        setIsTokenLoading(false);
      }
    };
    checkToken();
  }, []);

  const {data, isLoading, error, refetch} = useQuery<OrderType[] | null>({
    queryKey: ['orders'],
    queryFn: () => retrieveOrdersFromServerSide(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: hasToken && !isTokenLoading,
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
  });

  return {
    orders: data || [],
    ordersAreLoading: isLoading || isTokenLoading,
    ordersError: error,
    refetchOrders: refetch,
    hasToken,
    isTokenLoading,
  };
}
